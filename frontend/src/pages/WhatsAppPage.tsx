import React, { useState, useEffect, useRef } from 'react'
import {
  Search,
  MoreVertical,
  Phone,
  Video,
  Paperclip,
  Smile,
  Send,
  Settings,
  Plus,
  MessageCircle,
  Users,
  Archive,
  Star,
  Trash2,
  Volume2,
  VolumeX,
  Check,
  CheckCheck,
  Clock,
  Image,
  FileText,
  Mic,
  Calendar,
  UserPlus,
  X,
  ArrowLeft,
  Info,
  CheckSquare,
  BellOff,
  Timer,
  Heart,
  XCircle,
  Minus,
  LogOut,
  Filter,
  Camera,
  Play
} from 'lucide-react'

interface WhatsAppAccount {
  id: string
  name: string
  phone: string
  profilePicture?: string
  isActive: boolean
  status: 'connected' | 'disconnected' | 'connecting'
  lastSeen?: string
  userId: string
}

interface Contact {
  id: string
  name: string
  phone: string
  profilePicture?: string
  lastSeen?: string
  isOnline: boolean
  status?: string
}

interface Message {
  id: string
  contactId: string
  content: string
  timestamp: Date
  type: 'text' | 'image' | 'document' | 'audio' | 'video' | 'contact' | 'event'
  isFromMe: boolean
  status: 'sent' | 'delivered' | 'read'
  attachmentUrl?: string
  attachmentName?: string
}

interface Conversation {
  id: string
  contact: Contact
  lastMessage: Message
  unreadCount: number
  isPinned: boolean
  isArchived: boolean
  isMuted: boolean
  isStarred: boolean
}

interface BroadcastList {
  id: string
  name: string
  contacts: Contact[]
  createdAt: Date
}

const WhatsAppPage = () => {
  const [accounts, setAccounts] = useState<WhatsAppAccount[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const [selectedAccount, setSelectedAccount] = useState<WhatsAppAccount | null>(null)
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [messageSearchQuery, setMessageSearchQuery] = useState('')
  const [showAccountSettings, setShowAccountSettings] = useState(false)
  const [showNewChat, setShowNewChat] = useState(false)
  const [showAddAccount, setShowAddAccount] = useState(false)
  const [showAttachments, setShowAttachments] = useState(false)
  const [showChatMenu, setShowChatMenu] = useState(false)
  const [showMainMenu, setShowMainMenu] = useState(false)
  const [showBroadcast, setShowBroadcast] = useState(false)
  const [activeFilter, setActiveFilter] = useState<'all' | 'unread' | 'favorites' | 'groups' | 'archived'>('all')
  const [contacts, setContacts] = useState<Contact[]>([])
  const [broadcastLists, setBroadcastLists] = useState<BroadcastList[]>([])
  const [newAccountData, setNewAccountData] = useState({ name: '', phone: '' })

  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Load accounts and conversations
  useEffect(() => {
    loadAccounts()
    loadContacts()
  }, [])

  useEffect(() => {
    if (selectedAccount) {
      loadConversations()
    }
  }, [selectedAccount])

  const loadAccounts = async () => {
    try {
      setIsLoading(true)
      // In a real app, this would fetch from your API
      const mockAccounts: WhatsAppAccount[] = [
        {
          id: '1',
          name: 'Business Account',
          phone: '+**********',
          isActive: true,
          status: 'connected',
          lastSeen: '2 minutes ago',
          userId: 'user1'
        },
        {
          id: '2',
          name: 'Personal Account',
          phone: '+**********',
          isActive: false,
          status: 'disconnected',
          lastSeen: '1 hour ago',
          userId: 'user1'
        }
      ]
      setAccounts(mockAccounts)
      if (mockAccounts.length > 0) {
        setSelectedAccount(mockAccounts[0])
      }
    } catch (error) {
      console.error('Error loading accounts:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadContacts = async () => {
    try {
      // Mock contacts data
      const mockContacts: Contact[] = [
        {
          id: '1',
          name: 'John Doe',
          phone: '+**********',
          isOnline: true,
          status: 'Available'
        },
        {
          id: '2',
          name: 'Jane Smith',
          phone: '+**********',
          isOnline: false,
          lastSeen: '1 hour ago',
          status: 'Hey there! I am using WhatsApp.'
        },
        {
          id: '3',
          name: 'Mike Johnson',
          phone: '+**********',
          isOnline: true,
          status: 'Busy'
        }
      ]
      setContacts(mockContacts)
    } catch (error) {
      console.error('Error loading contacts:', error)
    }
  }

  const loadConversations = async () => {
    if (!selectedAccount) return

    try {
      // In a real app, this would fetch from your API
      const mockConversations: Conversation[] = [
        {
          id: '1',
          contact: {
            id: '1',
            name: 'John Doe',
            phone: '+**********',
            isOnline: true
          },
          lastMessage: {
            id: '1',
            contactId: '1',
            content: 'Hey, how are you?',
            timestamp: new Date(Date.now() - 5 * 60 * 1000),
            type: 'text',
            isFromMe: false,
            status: 'delivered'
          },
          unreadCount: 2,
          isPinned: false,
          isArchived: false,
          isMuted: false,
          isStarred: false
        },
        {
          id: '2',
          contact: {
            id: '2',
            name: 'Jane Smith',
            phone: '+**********',
            isOnline: false,
            lastSeen: '1 hour ago'
          },
          lastMessage: {
            id: '2',
            contactId: '2',
            content: 'Thanks for the information!',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            type: 'text',
            isFromMe: true,
            status: 'read'
          },
          unreadCount: 0,
          isPinned: true,
          isArchived: false,
          isMuted: false,
          isStarred: true
        },
        {
          id: '3',
          contact: {
            id: '3',
            name: 'Mike Johnson',
            phone: '+**********',
            isOnline: true
          },
          lastMessage: {
            id: '3',
            contactId: '3',
            content: 'See you tomorrow!',
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
            type: 'text',
            isFromMe: true,
            status: 'read'
          },
          unreadCount: 0,
          isPinned: false,
          isArchived: true,
          isMuted: true,
          isStarred: false
        }
      ]
      setConversations(mockConversations)
    } catch (error) {
      console.error('Error loading conversations:', error)
    }
  }

  // Mock messages for selected conversation
  useEffect(() => {
    if (selectedConversation) {
      const mockMessages: Message[] = [
        {
          id: '1',
          contactId: selectedConversation.contact.id,
          content: 'Hey, how are you?',
          timestamp: new Date(Date.now() - 10 * 60 * 1000),
          type: 'text',
          isFromMe: false,
          status: 'delivered'
        },
        {
          id: '2',
          contactId: selectedConversation.contact.id,
          content: 'I\'m doing great! How about you?',
          timestamp: new Date(Date.now() - 8 * 60 * 1000),
          type: 'text',
          isFromMe: true,
          status: 'read'
        },
        {
          id: '3',
          contactId: selectedConversation.contact.id,
          content: 'All good here. Are we still meeting tomorrow?',
          timestamp: new Date(Date.now() - 5 * 60 * 1000),
          type: 'text',
          isFromMe: false,
          status: 'delivered'
        }
      ]
      setMessages(mockMessages)
    }
  }, [selectedConversation])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedConversation) return

    const message: Message = {
      id: Date.now().toString(),
      contactId: selectedConversation.contact.id,
      content: newMessage,
      timestamp: new Date(),
      type: 'text',
      isFromMe: true,
      status: 'sent'
    }

    setMessages(prev => [...prev, message])
    setNewMessage('')
  }

  const getMessageStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Check className="h-4 w-4 text-gray-400" />
      case 'delivered':
        return <CheckCheck className="h-4 w-4 text-gray-400" />
      case 'read':
        return <CheckCheck className="h-4 w-4 text-blue-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }

  const filteredConversations = conversations.filter(conv =>
    conv.contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.contact.phone.includes(searchQuery)
  )

  return (
    <div className="h-screen bg-gray-100 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <MessageCircle className="h-8 w-8 text-green-600 mr-3" />
              WhatsApp Business
            </h1>
            <p className="text-sm text-gray-600 mt-1">
              Manage your WhatsApp conversations and accounts
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowAccountSettings(!showAccountSettings)}
              className="flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Settings className="h-4 w-4 mr-2" />
              Accounts ({accounts.length})
            </button>
          </div>
        </div>
      </div>

      {/* Account Settings Panel */}
      {showAccountSettings && (
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">WhatsApp Accounts</h3>
            <button className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Account
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {accounts.map(account => (
              <div key={account.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                      <MessageCircle className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{account.name}</p>
                      <p className="text-xs text-gray-500">{account.phone}</p>
                    </div>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    account.status === 'connected' 
                      ? 'bg-green-100 text-green-800' 
                      : account.status === 'connecting'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {account.status}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Main Chat Interface */}
      <div className="flex-1 flex overflow-hidden">
        {/* Conversations Sidebar */}
        <div className="w-1/3 bg-white border-r border-gray-200 flex flex-col">
          {/* Search */}
          <div className="p-4 border-b border-gray-200">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
            </div>
          </div>

          {/* Conversations List */}
          <div className="flex-1 overflow-y-auto">
            {filteredConversations.map(conversation => (
              <div
                key={conversation.id}
                onClick={() => setSelectedConversation(conversation)}
                className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                  selectedConversation?.id === conversation.id ? 'bg-green-50 border-l-4 border-l-green-500' : ''
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center flex-1 min-w-0">
                    <div className="relative">
                      <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
                        <Users className="h-6 w-6 text-gray-500" />
                      </div>
                      {conversation.contact.isOnline && (
                        <div className="absolute bottom-0 right-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></div>
                      )}
                    </div>
                    <div className="ml-3 flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {conversation.contact.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatTime(conversation.lastMessage.timestamp)}
                        </p>
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-sm text-gray-600 truncate">
                          {conversation.lastMessage.isFromMe && 'You: '}
                          {conversation.lastMessage.content}
                        </p>
                        {conversation.unreadCount > 0 && (
                          <span className="bg-green-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                            {conversation.unreadCount}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {selectedConversation ? (
            <>
              {/* Chat Header */}
              <div className="bg-white border-b border-gray-200 px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <Users className="h-5 w-5 text-gray-500" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">
                        {selectedConversation.contact.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {selectedConversation.contact.isOnline 
                          ? 'Online' 
                          : `Last seen ${selectedConversation.contact.lastSeen || 'recently'}`
                        }
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                      <Phone className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                      <Video className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                      <MoreVertical className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
                {messages.map(message => (
                  <div
                    key={message.id}
                    className={`flex ${message.isFromMe ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.isFromMe
                          ? 'bg-green-500 text-white'
                          : 'bg-white text-gray-900 border border-gray-200'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                      <div className={`flex items-center justify-end mt-1 space-x-1 ${
                        message.isFromMe ? 'text-green-100' : 'text-gray-500'
                      }`}>
                        <span className="text-xs">{formatTime(message.timestamp)}</span>
                        {message.isFromMe && getMessageStatusIcon(message.status)}
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <div className="bg-white border-t border-gray-200 px-4 py-4">
                <div className="flex items-center space-x-3">
                  <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                    <Paperclip className="h-5 w-5" />
                  </button>
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                      placeholder="Type a message..."
                      className="w-full px-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    />
                    <button className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600">
                      <Smile className="h-5 w-5" />
                    </button>
                  </div>
                  <button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim()}
                    className="p-2 bg-green-500 text-white rounded-full hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    <Send className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Select a conversation
                </h3>
                <p className="text-gray-600">
                  Choose a conversation from the sidebar to start messaging
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default WhatsAppPage
